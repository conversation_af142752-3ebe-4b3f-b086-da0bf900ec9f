{"name": "场景金融AI实验室", "version": "0.0.1", "description": "场景金融AI实验室", "dependencies": {"@ali/iec-dtao-utils": "^0.0.9", "@ice/plugin-request": "^1.0.3", "@ice/runtime": "^1.4.9", "antd": "^5.25.4", "axios": "^1.10.0", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21"}, "devDependencies": {"@ali/eslint-config-fin": "^0.0.16", "@ali/fin-lint": "^1.1.11", "@ali/ice-plugin-def": "^1.2.3", "@ali/ice-plugin-spm": "^2.1.0", "@ali/tnpm-lock-adapter": "^1.9.0", "@applint/spec": "^1.2.3", "@ice/app": "^3.4.9", "@types/node": "^18.11.17", "eslint": "^7.32.0", "husky": "^0.14.3", "react": "18.2.0", "react-dom": "18.2.0", "typescript": "^4.9.5"}, "engines": {"node": "16.17.1"}, "scripts": {"local": "tnpm start -- --config proxy.config.mts", "start": "ice start --mode local", "build:daily": "ice build --mode daily", "build": "ice build --mode prod", "eslint": "eslint ./src --cache --ext .js,.jsx,.ts,.tsx", "eslint:fix": "npm run eslint -- --fix", "stylelint": "stylelint \"src/**/*.{css,scss,less}\" --cache", "stylelint:fix": "npm run stylelint -- --fix", "lint": "mad lint", "precommit": "mad lint --staged", "postcommit": "mad lint --postcommit", "prepare": "tnpm-lock-adapter"}, "publishConfig": {"access": "public"}, "repository": "**************:ice-lab/react-materials.git"}