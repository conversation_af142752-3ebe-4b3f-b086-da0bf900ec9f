{
  "compilerOptions": {
    "baseUrl": "./",
    "module": "ESNext",
    "target": "ESNext",
    "lib": ["DOM", "ESNext", "DOM.Iterable"],
    "jsx": "react-jsx",
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noImplicitAny": false,
    "importHelpers": true,
    "strictNullChecks": true,
    // 这个是允许用a['b']的方式访问对象的值，但是很快就不支持了，所以关闭
    // "suppressImplicitAnyIndexErrors": true,
    "skipLibCheck": true,
    "paths": {
      "@/*": ["./src/*"],
      "ice": [".ice"]
    }
  },
  "include": ["src", ".ice"],
  "exclude": ["build"]
}
