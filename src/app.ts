import { defineAppConfig } from 'ice';
import { defineSpmConfig } from '@ali/ice-plugin-spm/types';
import request from '@ice/plugin-request';


// App config, see https://ice3.alibaba-inc.com/v3/docs/guide/basic/app
export default defineAppConfig(() => ({
  plugins: [
    request(),
  ],
  router: {
    type: 'hash',
  },
}));

// 设置应用SPM的A位
export const spmConfig = defineSpmConfig(() => {
  return {
    spmA: 'a360n',
  };
});
