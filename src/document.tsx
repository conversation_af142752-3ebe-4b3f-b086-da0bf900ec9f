import { Meta, Title, <PERSON><PERSON>, Main, Scripts } from 'ice';

export default function Document() {
  return (
    <html>
      <head>
        <meta charSet="utf-8" />
        <meta name="description" content="ice.js 3 lite scaffold" />
        <link rel="icon" href="/favicon.ico" />
        <meta name="aplus-waiting" content="MAN" />
        <meta name="data-spm" content="a360n" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
        <Meta />
        <Title />
        <Links />
        <script crossOrigin="" src="//g.alicdn.com/code/npm/@alife/fin-report-sdk/1.0.18/index.js" />
      </head>
      <body>
        <Main />
        <script crossOrigin="" src="https://g.alicdn.com/code/lib/react/18.2.0/umd/react.production.min.js" />
        <script crossOrigin="" src="https://g.alicdn.com/code/lib/react-dom/18.2.0/umd/react-dom.production.min.js" />
        <Scripts />
      </body>
    </html>
  );
}
