.app {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.app > header {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.app > header > img {
  width: 120px;
}

.app > header > p {
  margin: 20px 0;
  text-align: center;
  font-size: 2.6rem;
}

.app > main {
  display: flex;
  flex-direction: column;
  margin: 20px 0 10px;
  font-size: 0.9rem;
}

.link {
  font-size: 1.2rem;
  color: var(--primary);
}

.demo-logo-vertical {
  width: 100%;
}

.demo-logo-vertical .title {
  font-size: 18px;
  color: #fff;
  font-weight: 600;
  padding: 18px 18px;
  margin: 0;
}

.divider {
  border-color: #fff;
  margin: 12px 0;
}

.item {
  display: flex;
  align-items: center;
  label {
    flex: 0.3;
  }
  .value {
    flex: 0.7;
  }
}
