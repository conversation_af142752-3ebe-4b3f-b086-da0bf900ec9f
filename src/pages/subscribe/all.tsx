/**
 * @file 推送记录
 * <AUTHOR>
 */

import React, { useState, useEffect } from 'react';
import { Table } from 'antd';
import { ajax } from '@ali/iec-dtao-utils';
import { HOST } from '../../constant';

import './index.css';

const columns = [
  {
    title: '产品',
    dataIndex: 'product',
    key: 'product',
  },
  {
    title: '推送人',
    dataIndex: 'employId',
    key: 'employId',
  },
  {
    title: '推送标题',
    dataIndex: 'title',
    key: 'title',
  },
  {
    title: '推送内容',
    dataIndex: 'content',
    key: 'content',
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
  },
  {
    title: '推送方式',
    dataIndex: 'notifyType',
    key: 'notifyType',
  },
  {
    title: '推送指标',
    dataIndex: 'metricId',
    key: 'metricId',
  },
  {
    title: '订阅名称',
    dataIndex: 'subscriptionName',
    key: 'subscriptionName',
  },
  {
    title: '推送地址',
    dataIndex: 'notifyAddress',
    key: 'notifyAddress',
  },
];

export default function All() {
  const [list, setList] = useState([]);

  const getList = async () => {
    const res: any = await ajax.get({
      url: `https://${HOST}/f/v2/fin-one/iec_ai_getLatestNotificationLogs`,
    });
    setList(res?.data);
  };

  useEffect(() => {
    getList();
  }, []);

  return (
    <div className="my">
      <Table columns={columns} dataSource={list} />
    </div>
  );
}
