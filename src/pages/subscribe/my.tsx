/**
 * @file 我的订阅
 * <AUTHOR>
 */

import React, { useState, useEffect } from 'react';
import { Table } from 'antd';
import { ajax } from '@ali/iec-dtao-utils';
import { HOST } from '../../constant';

import './index.css';

const columns = [
  {
    title: '产品',
    dataIndex: 'product',
    key: 'product',
  },
  {
    title: '订阅名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '推送频率',
    dataIndex: 'reportType',
    key: 'reportType',
  },
  {
    title: '推送地址',
    dataIndex: 'notifyAddress',
    key: 'notifyAddress',
  },
];

export default function My() {
  const [list, setList] = useState([]);

  const getList = async () => {
    const res: any = await ajax.get({
      url: `https://${HOST}/f/v2/fin-one/iec_ai_getSubscriptionsByEmployId`,
    });
    setList(res?.data);
  };

  useEffect(() => {
    getList();
    setInterval(() => {
      getList();
    }, 2000);
  }, []);

  return (
    <div className="my">
      <Table columns={columns} dataSource={list} />
    </div>
  );
}
