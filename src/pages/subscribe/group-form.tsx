/**
 * @file 指标组订阅管理
 * <AUTHOR>
 */

import React, { useState, useEffect } from 'react';
import { Form, Input, Radio, Button, Modal, Checkbox, message } from 'antd';
import { ajax } from '@ali/iec-dtao-utils';
import { set } from 'lodash-es';
import { HOST } from '../../constant';

import './index.css';

export default function GroupForm() {
  const [form] = Form.useForm();
  const [groupsList, setGroupsList] = useState([]);
  const [loading, setLoading] = useState(false);

  // 查询所有指标组
  useEffect(() => {
    const fetchData = async () => {
      const groupsRes: any = await ajax.get({ 
        url: `https://${HOST}/f/v2/fin-one/iec_ai_getAllGroups` 
      });
      setGroupsList(groupsRes?.data || []);
    };
    fetchData();
  }, []);

  const handleSubmit = async (values: any) => {
    const { dropAlertDimensions, dropThresholds, includeCompareData, enableDropAlert, groupIds, ...other } = values;
    
    // 验证是否选择了指标组
    if (!groupIds || groupIds.length === 0) {
      message.error('请至少选择一个指标组');
      return;
    }

    setLoading(true);
    
    const obj = {};
    if (dropThresholds) {
      set(obj, dropAlertDimensions, +dropThresholds);
    }
    
    const formData = {
      ...other,
      selectionType: 'groups', // 明确标识为指标组订阅
      selectedItems: groupIds,
      dropAlertDimensions: [dropAlertDimensions],
      dropThresholds: obj,
      includeCompareData: includeCompareData === 'true',
      enableDropAlert: enableDropAlert === 'true',
    };
    
    try {
      const res: any = await ajax.get({
        url: `https://${HOST}/f/v2/fin-one/iec_ai_addSubscription`,
        data: {
          data: JSON.stringify(formData),
        },
      });
      
      if (res?.success) {
        message.success('订阅指标组成功');
        form.resetFields();
      } else {
        message.error('订阅失败，请重试');
      }
    } catch (error) {
      message.error('订阅失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const enableDropAlertValue = Form.useWatch('enableDropAlert', form);

  return (
    <div className="mertic">
      <Form layout="horizontal" form={form} onFinish={handleSubmit}>
        <Form.Item label="产品" name="product">
          <Input placeholder="请输入产品名称" />
        </Form.Item>
        
        <Form.Item label="订阅名称" name="name">
          <Input placeholder="请输入订阅名称" />
        </Form.Item>
        
        <Form.Item label="推送频率" name="reportType">
          <Radio.Group>
            <Radio.Button value="daily">每日</Radio.Button>
            <Radio.Button value="weekly">每周</Radio.Button>
            <Radio.Button value="monthly">每月</Radio.Button>
          </Radio.Group>
        </Form.Item>

        <Form.Item label="指标组" name="groupIds">
          <Checkbox.Group
            options={groupsList.map((item: any) => ({ 
              key: item.id,
              label: item.name, 
              value: item.id 
            }))}
          />
        </Form.Item>

        <Form.Item label="推送渠道" name="notifyType">
          <Radio.Group>
            <Radio.Button value="ding">钉钉</Radio.Button>
            <Radio.Button value="mail">邮件</Radio.Button>
          </Radio.Group>
        </Form.Item>
        
        <Form.Item label="推送地址" name="notifyAddress">
          <Input placeholder="请输入推送地址" />
        </Form.Item>
        
        <Form.Item label="下跌提醒" name="enableDropAlert">
          <Radio.Group>
            <Radio.Button value="true">开启</Radio.Button>
            <Radio.Button value="false">关闭</Radio.Button>
          </Radio.Group>
        </Form.Item>
        
        {enableDropAlertValue === 'true' ? (
          <>
            <Form.Item label="下跌维度" name="dropAlertDimensions">
              <Radio.Group>
                <Radio.Button value="dod">日环比</Radio.Button>
                <Radio.Button value="wow">周环比</Radio.Button>
                <Radio.Button value="mom">月环比</Radio.Button>
                <Radio.Button value="yoy">年环比</Radio.Button>
              </Radio.Group>
            </Form.Item>
            <Form.Item label="阈值设置" name="dropThresholds">
              <Input placeholder="请输入阈值" />
            </Form.Item>
          </>
        ) : null}
        
        <Form.Item label="包含对比数据" name="includeCompareData">
          <Radio.Group>
            <Radio.Button value="true">开启</Radio.Button>
            <Radio.Button value="false">关闭</Radio.Button>
          </Radio.Group>
        </Form.Item>
        
        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>
            提交
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
}
