/**
 * @file 指标管理
 * <AUTHOR>
 */

import React, { useState, useEffect } from 'react';
import { Form, Input, Radio, Button, Modal } from 'antd';
import { ajax } from '@ali/iec-dtao-utils';
import { set } from 'lodash-es';
import { HOST } from '../../constant';

import './index.css';

export default function Mertic() {
  const [form] = Form.useForm();
  const [list, setList] = useState([]);
  const enableDropAlertValue = Form.useWatch('enableDropAlert', form);

  const handleSubmit = async (values: any) => {
    const { dropAlertDimensions, dropThresholds, includeCompareData, enableDropAlert, ...other } = values;
    console.log('values',values);
    const obj = {};
    if (dropThresholds) {
      set(obj, dropAlertDimensions, +dropThresholds);
    }
    const formData = {
      ...other,
      dropAlertDimensions: [dropAlertDimensions],
      dropThresholds: obj,
      includeCompareData: includeCompareData === 'true',
      enableDropAlert: enableDropAlert === 'true',
    };
    console.log('formData',formData);
    const res: any = await ajax.get({
      url: `https://${HOST}/f/v2/fin-one/iec_ai_addSubscription`,
      data: {
        data: JSON.stringify(formData),
      },
    });
    if (res?.success) {
      Modal.success({
        content: '提交成功',
      });
    }
  };

  const getList = async () => {
    const res: any = await ajax.get({
      url: `https://${HOST}/f/v2/fin-one/iec_ai_getAllMetrics`,
    });
    const groupRes: any = await ajax.get({
      url: `https://${HOST}/f/v2/fin-one/iec_ai_getAllGroups`,
    });
    setList(res?.data);
  };

  const renderList = () => {
    return list?.map((item) => {
      return <Radio.Button value={item.id}>{item.name}</Radio.Button>;
    });
  };

  useEffect(() => {
    getList();
  }, []);

  return (
    <div className="mertic">
      <Form
        layout="horizontal"
        form={form}
        onFinish={handleSubmit}
      >
        <Form.Item label="产品" name="product">
          <Input />
        </Form.Item>
        <Form.Item label="订阅名称" name="name">
          <Input />
        </Form.Item>
        <Form.Item label="推送频率" name="reportType">
          <Radio.Group>
            <Radio.Button value="daily">每日</Radio.Button>
            <Radio.Button value="weekly">每周</Radio.Button>
            <Radio.Button value="monthly">每月</Radio.Button>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="指标" name="metricId">
          <Radio.Group>
            {renderList()}
          </Radio.Group>
        </Form.Item>
        <Form.Item label="推送渠道" name="notifyType">
          <Radio.Group>
            <Radio.Button value="daily">钉钉</Radio.Button>
            <Radio.Button value="monthly">邮件</Radio.Button>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="推送地址" name="notifyAddress">
          <Input />
        </Form.Item>
        <Form.Item label="下跌提醒" name="enableDropAlert">
          <Radio.Group>
            <Radio.Button value="true">开启</Radio.Button>
            <Radio.Button value="false">关闭</Radio.Button>
          </Radio.Group>
        </Form.Item>
        {enableDropAlertValue === 'true' ? (
          <>
            <Form.Item label="下跌维度" name="dropAlertDimensions">
              <Radio.Group>
                <Radio.Button value="dod">日环比</Radio.Button>
                <Radio.Button value="wow">周环比</Radio.Button>
                <Radio.Button value="mom">月环比</Radio.Button>
                <Radio.Button value="yoy">年环比</Radio.Button>
              </Radio.Group>
            </Form.Item>
            <Form.Item label="阈值设置" name="dropThresholds">
              <Input />
            </Form.Item>
          </>
        ) : null}
        <Form.Item label="包含对比数据" name="includeCompareData">
          <Radio.Group>
            <Radio.Button value="true">开启</Radio.Button>
            <Radio.Button value="false">关闭</Radio.Button>
          </Radio.Group>
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit">提交</Button>
        </Form.Item>
      </Form>
    </div>
  );
}
