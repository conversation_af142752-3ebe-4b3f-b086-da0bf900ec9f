/**
 * @file 指标管理
 * <AUTHOR>
 */

import React, { useState, useEffect } from 'react';
import { Form, Input, Radio, Button, Modal, Checkbox, Divider } from 'antd';
import { ajax } from '@ali/iec-dtao-utils';
import { set } from 'lodash-es';
import { HOST } from '../../constant';

import './index.css';

export default function Mertic() {
  const [form] = Form.useForm();
  const [list, setList] = useState([]);
  const [groupList, setGroupList] = useState([]);
  const [selectionType, setSelectionType] = useState<'metrics' | 'groups'>('metrics');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const enableDropAlertValue = Form.useWatch('enableDropAlert', form);

  const handleSubmit = async (values: any) => {
    const { dropAlertDimensions, dropThresholds, includeCompareData, enableDropAlert, selectionType: formSelectionType, selectedItems: formSelectedItems, ...other } = values;
    console.log('values',values);

    // 验证是否选择了指标或指标组
    if (!formSelectedItems || formSelectedItems.length === 0) {
      Modal.error({
        content: '请至少选择一个指标或指标组',
      });
      return;
    }

    const obj = {};
    if (dropThresholds) {
      set(obj, dropAlertDimensions, +dropThresholds);
    }

    const formData = {
      ...other,
      selectionType: formSelectionType,
      selectedItems: formSelectedItems,
      dropAlertDimensions: [dropAlertDimensions],
      dropThresholds: obj,
      includeCompareData: includeCompareData === 'true',
      enableDropAlert: enableDropAlert === 'true',
    };
    console.log('formData',formData);
    const res: any = await ajax.get({
      url: `https://${HOST}/f/v2/fin-one/iec_ai_addSubscription`,
      data: {
        data: JSON.stringify(formData),
      },
    });
    if (res?.success) {
      Modal.success({
        content: '提交成功',
      });
      // 重置表单和状态
      form.resetFields();
      setSelectedItems([]);
      setSelectionType('metrics');
    }
  };

  const getList = async () => {
    const res: any = await ajax.get({
      url: `https://${HOST}/f/v2/fin-one/iec_ai_getAllMetrics`,
    });
    const groupRes: any = await ajax.get({
      url: `https://${HOST}/f/v2/fin-one/iec_ai_getAllGroups`,
    });
    setList(res?.data);
    setGroupList(groupRes?.data);
  };

  const renderMetricsList = () => {
    return list?.map((item: any) => {
      return (
        <Checkbox key={item.id} value={item.id}>
          {item.name}
        </Checkbox>
      );
    });
  };

  const renderGroupsList = () => {
    return groupList?.map((item: any) => {
      return (
        <Checkbox key={item.id} value={item.id}>
          {item.name}
        </Checkbox>
      );
    });
  };

  const handleSelectionTypeChange = (type: 'metrics' | 'groups') => {
    setSelectionType(type);
    setSelectedItems([]);
    form.setFieldValue('selectedItems', []);
  };

  const handleItemsChange = (checkedValues: string[]) => {
    setSelectedItems(checkedValues);
  };

  const getCurrentList = () => {
    return selectionType === 'metrics' ? list : groupList;
  };

  const renderCurrentList = () => {
    return selectionType === 'metrics' ? renderMetricsList() : renderGroupsList();
  };

  useEffect(() => {
    getList();
  }, []);

  return (
    <div className="mertic">
      <Form
        layout="horizontal"
        form={form}
        onFinish={handleSubmit}
      >
        <Form.Item label="产品" name="product">
          <Input />
        </Form.Item>
        <Form.Item label="订阅名称" name="name">
          <Input />
        </Form.Item>
        <Form.Item label="推送频率" name="reportType">
          <Radio.Group>
            <Radio.Button value="daily">每日</Radio.Button>
            <Radio.Button value="weekly">每周</Radio.Button>
            <Radio.Button value="monthly">每月</Radio.Button>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="选择类型" name="selectionType" initialValue="metrics">
          <Radio.Group onChange={(e) => handleSelectionTypeChange(e.target.value)}>
            <Radio.Button value="metrics">指标</Radio.Button>
            <Radio.Button value="groups">指标组</Radio.Button>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          label={selectionType === 'metrics' ? '选择指标' : '选择指标组'}
          name="selectedItems"
        >
          <Checkbox.Group onChange={handleItemsChange}>
            <div style={{ marginBottom: '12px' }}>
              <Checkbox
                indeterminate={selectedItems.length > 0 && selectedItems.length < getCurrentList().length}
                onChange={(e) => {
                  const currentList = getCurrentList();
                  const allIds = currentList.map((item: any) => item.id);
                  const newValues = e.target.checked ? allIds : [];
                  setSelectedItems(newValues);
                  form.setFieldValue('selectedItems', newValues);
                }}
                checked={selectedItems.length === getCurrentList().length && getCurrentList().length > 0}
              >
                全选 ({selectedItems.length}/{getCurrentList().length})
              </Checkbox>
            </div>
            <Divider />
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', maxHeight: '200px', overflowY: 'auto' }}>
              {renderCurrentList()}
            </div>
          </Checkbox.Group>
        </Form.Item>
        <Form.Item label="推送渠道" name="notifyType">
          <Radio.Group>
            <Radio.Button value="daily">钉钉</Radio.Button>
            <Radio.Button value="monthly">邮件</Radio.Button>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="推送地址" name="notifyAddress">
          <Input />
        </Form.Item>
        <Form.Item label="下跌提醒" name="enableDropAlert">
          <Radio.Group>
            <Radio.Button value="true">开启</Radio.Button>
            <Radio.Button value="false">关闭</Radio.Button>
          </Radio.Group>
        </Form.Item>
        {enableDropAlertValue === 'true' ? (
          <>
            <Form.Item label="下跌维度" name="dropAlertDimensions">
              <Radio.Group>
                <Radio.Button value="dod">日环比</Radio.Button>
                <Radio.Button value="wow">周环比</Radio.Button>
                <Radio.Button value="mom">月环比</Radio.Button>
                <Radio.Button value="yoy">年环比</Radio.Button>
              </Radio.Group>
            </Form.Item>
            <Form.Item label="阈值设置" name="dropThresholds">
              <Input />
            </Form.Item>
          </>
        ) : null}
        <Form.Item label="包含对比数据" name="includeCompareData">
          <Radio.Group>
            <Radio.Button value="true">开启</Radio.Button>
            <Radio.Button value="false">关闭</Radio.Button>
          </Radio.Group>
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit">提交</Button>
        </Form.Item>
      </Form>
    </div>
  );
}
