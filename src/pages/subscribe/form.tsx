/**
 * @file 指标管理
 * <AUTHOR>
 */

import React, { useState, useEffect } from 'react';
import { Form, Input, Radio, Button, Modal, Checkbox, message } from 'antd';
import { ajax } from '@ali/iec-dtao-utils';
import { set } from 'lodash-es';
import { HOST } from '../../constant';

import './index.css';

export default function MetricOrGroupForm() {
  const [form] = Form.useForm();
  const [metricsList, setMetricsList] = useState([]);
  const [groupsList, setGroupsList] = useState([]);
  const [selectType, setSelectType] = useState<'metric' | 'group'>('metric');
  const [loading, setLoading] = useState(false);

  // 查询所有指标和指标组
  useEffect(() => {
    alert(123);
    const fetchData = async () => {
      const [metricsRes, groupsRes]: any[] = await Promise.all([
        ajax.get({ url: `https://${HOST}/f/v2/fin-one/iec_ai_getAllMetrics` }),
        ajax.get({ url: `https://${HOST}/f/v2/fin-one/iec_ai_getAllGroups` }),
      ]);
      setMetricsList(metricsRes?.data || []);
      setGroupsList(groupsRes?.data || []);
    };
    fetchData();
  }, []);

  // 选择类型切换时清空已选
  const handleTypeChange = (e) => {
    setSelectType(e.target.value);
    form.setFieldsValue({ metricIds: [], groupIds: [] });
  };

  const handleSubmit = async (values: any) => {
    const { dropAlertDimensions, dropThresholds, includeCompareData, enableDropAlert, ...other } = values;
    const obj = {};
    if (dropThresholds) {
      set(obj, dropAlertDimensions, +dropThresholds);
    }
    const formData = {
      ...other,
      dropAlertDimensions: [dropAlertDimensions],
      dropThresholds: obj,
      includeCompareData: includeCompareData === 'true',
      enableDropAlert: enableDropAlert === 'true',
    };
    const res: any = await ajax.get({
      url: `https://${HOST}/f/v2/fin-one/iec_ai_addSubscription`,
      data: {
        data: JSON.stringify(formData),
      },
    });
    if (res?.success) {
      Modal.success({
        content: '提交成功',
      });
    }
  };

  return (
    <div className="mertic">
      <Form layout="horizontal" form={form} onFinish={handleSubmit}>
        <Form.Item label="产品" name="product">
          <Input />
        </Form.Item>
        <Form.Item label="订阅名称" name="name">
          <Input />
        </Form.Item>
        <Form.Item label="推送频率" name="reportType">
          <Radio.Group>
            <Radio.Button value="daily">每日</Radio.Button>
            <Radio.Button value="weekly">每周</Radio.Button>
            <Radio.Button value="monthly">每月</Radio.Button>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="选择类型" name="selectType" initialValue="metric">
          <Radio.Group value={selectType} onChange={handleTypeChange}>
            <Radio.Button value="metric">指标</Radio.Button>
            <Radio.Button value="group">指标组</Radio.Button>
          </Radio.Group>
        </Form.Item>
        {selectType === 'metric' ? (
          <Form.Item label="指标" name="metricIds">
            <Checkbox.Group
              options={metricsList.map((item) => ({ label: item.name, value: item.id }))}
            />
          </Form.Item>
        ) : (
          <Form.Item label="指标组" name="groupIds">
            <Checkbox.Group
              options={groupsList.map((item) => ({ label: item.name, value: item.id }))}
            />
          </Form.Item>
        )}
        <Form.Item label="推送渠道" name="notifyType">
          <Radio.Group>
            <Radio.Button value="ding">钉钉</Radio.Button>
            <Radio.Button value="mail">邮件</Radio.Button>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="推送地址" name="notifyAddress">
          <Input />
        </Form.Item>
        <Form.Item label="下跌提醒" name="enableDropAlert">
          <Radio.Group>
            <Radio.Button value="true">开启</Radio.Button>
            <Radio.Button value="false">关闭</Radio.Button>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="包含对比数据" name="includeCompareData">
          <Radio.Group>
            <Radio.Button value="true">开启</Radio.Button>
            <Radio.Button value="false">关闭</Radio.Button>
          </Radio.Group>
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>提交</Button>
        </Form.Item>
      </Form>
    </div>
  );
}
