/**
 * @file 指标组管理
 * <AUTHOR>
 */
import React, { useState, useEffect } from 'react';
import { Button, Table, Modal, Form, Input, Space, message, Radio, Checkbox } from 'antd';
import { EditOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons';
import { ajax } from '@ali/iec-dtao-utils';
import { HOST } from '../../constant';

import './index.css';

interface MetricGroup {
  id?: number;
  name: string;
  description: string;
  metricIds: number[];
  frequency?: string;
  type?: string;
  createTime?: string;
  updateTime?: string;
}

const Group: React.FC = () => {
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [list, setList] = useState<MetricGroup[]>([]);
  const [isModalOpen, setModalOpen] = useState(false);
  const [editingGroup, setEditingGroup] = useState<MetricGroup | null>(null);
  const [loading, setLoading] = useState(false);

  // 频率选项
  const frequencyOptions = [
    { label: '每日', value: 'daily' },
    { label: '每周', value: 'weekly' },
    { label: '每月', value: 'monthly' }
  ];

  // 类型选项
  const typeOptions = [
    { label: '指标', value: 'metric' },
    { label: '指标组', value: 'group' }
  ];

  // 指标选项
  const metricOptions = [
    { label: '天猫回款订单量', value: 1 },
    { label: '天猫回款GMV', value: 2 },
    { label: '淘宝回款订单量', value: 3 }
  ];

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '指标组名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '指标组描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 180,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: MetricGroup) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 获取所有指标组
  const getAllGroups = async () => {
    setLoading(true);
    try {
      const res: any = await ajax.get({
        url: `https://${HOST}/f/v2/fin-one/iec_ai_getAllGroups`,
      });
      if (res?.success) {
        setList(res?.data || []);
      }
    } catch (error) {
      message.error('获取指标组列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 添加指标组
  const addGroup = async (values: MetricGroup) => {
    try {
      const res: any = await ajax.post({
        url: `https://${HOST}/f/v2/fin-one/iec_ai_addGroup`,
        data: values,
      });
      if (res?.success) {
        message.success('添加成功');
        setModalOpen(false);
        form.resetFields();
        getAllGroups();
      } else {
        message.error(res?.message || '添加失败');
      }
    } catch (error) {
      message.error('添加失败');
    }
  };

  // 更新指标组
  const updateGroup = async (values: MetricGroup) => {
    try {
      const res: any = await ajax.post({
        url: `https://${HOST}/f/v2/fin-one/iec_ai_updateGroup`,
        data: { ...values, id: editingGroup?.id },
      });
      if (res?.success) {
        message.success('更新成功');
        setModalOpen(false);
        form.resetFields();
        setEditingGroup(null);
        getAllGroups();
      } else {
        message.error(res?.message || '更新失败');
      }
    } catch (error) {
      message.error('更新失败');
    }
  };
  // 表单查询
  const getGroupById = async (values: number) => {
    try {
      const res: any = await ajax.post({
        url: `https://${HOST}/f/v2/fin-one/iec_ai_getGroupById`,
        data: { values, id: editingGroup?.id },
      });
      if (res?.success) {
        message.success('更新成功');
        setModalOpen(false);
        form.resetFields();
        setEditingGroup(null);
        getAllGroups();
      } else {
        message.error(res?.message || '更新失败');
      }
    } catch (error) {
      message.error('更新失败');
    }
  };
  const searchGroupsByName = async (name:string) => {
    try {
      const res: any = await ajax.post({
        url: `https://${HOST}/f/v2/fin-one/iec_ai_searchGroupsByName`,
        data: { name, id: editingGroup?.id },
      });
      if (res?.success) {
        message.success('更新成功');
        setModalOpen(false);
        form.resetFields();
        setEditingGroup(null);
        getAllGroups();
      } else {
        message.error(res?.message || '更新失败');
      }
    } catch (error) {
      message.error('更新失败');
    }
  };

  // 表单提交处理
  const handleSubmit = async (values: MetricGroup) => {
    if (editingGroup) {
      await updateGroup(values);
    } else {
      await addGroup(values);
    }
  };

  // 编辑处理
  const handleEdit = async (record: MetricGroup) => {
    const groupDetail = await getGroupById(record.id!);
    if (groupDetail !== null) {
      setEditingGroup(groupDetail);
      form.setFieldsValue(groupDetail);
      setModalOpen(true);
    }
  };

  // 删除处理
  const handleDelete = (record: MetricGroup) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除指标组"${record.name}"吗？`,
      onOk: async () => {
        try {
          const res: any = await ajax.post({
            url: `https://${HOST}/f/v2/fin-one/iec_ai_deleteGroup`,
            data: { id: record.id },
          });
          if (res?.success) {
            message.success('删除成功');
            getAllGroups();
          } else {
            message.error(res?.message || '删除失败');
          }
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  // 搜索处理
  const handleSearch = (values: { name: string }) => {
    searchGroupsByName(values.name);
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    getAllGroups();
  };

  // 打开添加弹窗
  const handleAdd = () => {
    setEditingGroup(null);
    form.resetFields();
    setModalOpen(true);
  };

  // 弹窗确定
  const handleOk = () => {
    form.submit();
  };

  // 弹窗取消
  const handleCancel = () => {
    setModalOpen(false);
    setEditingGroup(null);
    form.resetFields();
  };

  useEffect(() => {
    getAllGroups();
  }, []);

  return (
    <div className="group">
      {/* 搜索区域 */}
      <div className="search-area" style={{ marginBottom: 16 }}>
        <Form form={searchForm} layout="inline" onFinish={handleSearch}>
          <Form.Item name="name" label="指标组名称">
            <Input placeholder="请输入指标组名称" style={{ width: 200 }} />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                搜索
              </Button>
              <Button onClick={handleReset}>重置</Button>
            </Space>
          </Form.Item>
        </Form>
      </div>

      {/* 操作区域 */}
      <div className="hd" style={{ marginBottom: 16 }}>
        <Button type="primary" onClick={handleAdd}>
          添加指标组
        </Button>
      </div>

      {/* 表格 */}
      <Table
        columns={columns}
        dataSource={list}
        loading={loading}
        rowKey="id"
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
        }}
      />

      {/* 添加/编辑弹窗 */}
      <Modal
        title={editingGroup ? '编辑指标组' : '添加指标组'}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="确定"
        cancelText="取消"
        width={600}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            type: 'metric',
            frequency: 'daily'
          }}
        >
          {/* 产品 */}
          <Form.Item label="产品">
            <Input value="订单回款" disabled />
          </Form.Item>

          {/* 订阅名称 */}
          <Form.Item
            label="订阅名称"
            name="name"
            rules={[
              { required: true, message: '请输入订阅名称' },
              { max: 50, message: '订阅名称不能超过50个字符' }
            ]}
          >
            <Input placeholder="订单页ocr优化" />
          </Form.Item>

          {/* 推送频率 */}
          <Form.Item
            label="推送频率"
            name="frequency"
            rules={[{ required: true, message: '请选择推送频率' }]}
          >
            <Radio.Group options={frequencyOptions} optionType="button" />
          </Form.Item>

          {/* 选择类型 */}
          <Form.Item
            label="选择类型"
            name="type"
            rules={[{ required: true, message: '请选择类型' }]}
          >
            <Radio.Group options={typeOptions} optionType="button" />
          </Form.Item>

          {/* 指标 */}
          <Form.Item
            label="指标"
            name="metricIds"
            rules={[{ required: true, message: '请选择指标' }]}
          >
            <Checkbox.Group options={metricOptions} />
          </Form.Item>

          {/* 描述信息 */}
          <Form.Item
            label="描述信息"
            name="description"
            rules={[
              { required: true, message: '请输入描述信息' },
              { max: 200, message: '描述信息不能超过200个字符' }
            ]}
          >
            <Input.TextArea rows={4} placeholder="请输入描述信息" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Group;
