/**
 * @file 指标管理
 * <AUTHOR>
 */

import React, { useState, useEffect } from 'react';
import { Button, Table, Modal, Form, Input } from 'antd';
import { ajax } from '@ali/iec-dtao-utils';
import { HOST } from '../../constant';

import './index.css';

const columns = [
  {
    title: '产品',
    dataIndex: 'product',
    key: 'product',
  },
  {
    title: '指标模版',
    dataIndex: 'fetchLogicTemplate',
    key: 'fetchLogicTemplate',
  },
  {
    title: '指标名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '指标描述',
    dataIndex: 'description',
    key: 'description',
  },
];

export default function Mertic() {
  const [form] = Form.useForm();
  const [list, setList] = useState([]);
  const [isModalOpen, setModalOpen] = useState(false);

  const handleSubmit = async (values: any) => {
    const res: any = await ajax.get({
      url: `https://${HOST}/f/v2/fin-one/iec_ai_addMetric`,
      data: values,
    });
    if (res?.success) {
      Modal.success({
        content: '提交成功',
      });
    }
  };

  const getList = async () => {
    const res: any = await ajax.get({
      url: `https://${HOST}/f/v2/fin-one/iec_ai_getAllMetrics`,
    });
    setList(res?.data);
  };

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    setModalOpen(false);
  };

  const handleOnClick = () => {
    setModalOpen(true);
  };

  useEffect(() => {
    getList();
    setInterval(() => {
      getList();
    }, 2000);
  }, []);

  return (
    <div className="mertic">
      <div className="hd">
        <Button onClick={handleOnClick} color="primary">添加</Button>
      </div>
      <Table columns={columns} dataSource={list} />
      <Modal
        title="授权"
        closable={{ 'aria-label': 'Custom Close Button' }}
        open={isModalOpen}
        onOk={handleOk}
        okText="确定"
        cancelText="取消"
        onCancel={handleCancel}
      >
        <Form
          layout="horizontal"
          form={form}
          onFinish={handleSubmit}
        >
          <Form.Item label="产品" name="product">
            <Input />
          </Form.Item>
          <Form.Item label="指标模版" name="fetchLogicTemplate">
            <Input placeholder="" />
          </Form.Item>
          <Form.Item label="指标名称" name="name">
            <Input placeholder="" />
          </Form.Item>
          <Form.Item label="指标描述" name="description">
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
