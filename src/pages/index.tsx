import React, { useState, useEffect } from 'react';
import { definePageConfig } from 'ice';
import { PieChartOutlined } from '@ant-design/icons';
import type { MenuProps, TableProps } from 'antd';
import { Layout, Menu, theme, Divider, Space, Table, Tag, Modal, Input, Tabs } from 'antd';
import { ajax } from '@ali/iec-dtao-utils';

import Mertic from './subscribe/mertic';
import MyForm from './subscribe/form';
import Group from './subscribe/group';
import MyAll from './subscribe/all';
import My from './subscribe/my';
import { HOST } from '../constant';

import './index.css';

const { Content, Footer, Sider } = Layout;

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[],
): MenuItem {
  return { key, icon, children, label } satisfies MenuItem;
}

const items2: MenuItem[] = [
  getItem('权限管理', '权限管理', <PieChartOutlined />),
  getItem('订阅管理', '订阅管理', <PieChartOutlined />),
  getItem('帮助中心', '帮助中心', <PieChartOutlined />),
];

const items = [
  {
    key: 'mertic',
    label: '指标管理',
    children: <Mertic />,
  },
  {
    key: 'form',
    label: '添加订阅',
    children: <MyForm />,
  },
  {
    key: 'group',
    label: '订阅指标组',
    children: <Group />,
  },
  {
    key: 'all',
    label: '推送记录',
    children: <MyAll />,
  },
  {
    key: 'my',
    label: '我的订阅',
    children: <My />,
  },
];


interface DataType {
  key: string;
  name: string;
  age: string;
  address: string;
  tags: string[];
  users: string[];
}

const SCENE_MAP = {
  disburse_ai_ops: '回小宝',
  loan_ai_ops: '贷小宝',
  zzfw_ai_ops: '增小宝',
  circle_ai_ops: '圈小宝',
  byf_ai_ops: '退小宝',
  ticket_ai_ops: '单小宝',
};

export const pageConfig = definePageConfig(() => ({
  spm: {
    spmB: 'index',
  },
}));

export default function Home() {
  const [collapsed, setCollapsed] = useState(false);
  const [side, setSide] = useState({
    key: 'AI工单助手',
  });
  const [my, setMy] = useState({
    key: '权限管理',
  });
  const [authors, setAuthors] = useState([]);
  const [adminPayload, setAdminPayload] = useState<any>();
  const [listPayload, setListPayload] = useState<any>();
  const [newList, setNewList] = useState<any>();
  const [scenes, setScenes] = useState<any>();
  const [delPayload, setDelPayload] = useState<any>();
  console.log(listPayload);
  const [payload, setPayload] = useState<any>({
    scene: '',
    empId: '',
  });
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();
  const [isModalOpen, setModalOpen] = useState(false);
  const [isModalOpen1, setModalOpen1] = useState(false);

  const handleClick = (value: any) => {
    setPayload({
      scene: value?.scene,
      name: value?.name,
    });
    setModalOpen(true);
  };

  const handleValueChange = (value: any) => {
    setPayload({
      ...payload,
      empId: value?.target?.value,
    });
  };

  const queryScenes = async (scene: string) => {
    const res: any = await ajax.get({
      url: `https://${HOST}/f/v2/fin-one/iec_ai_getEmployIdsByScene`,
      data: {
        scene,
      },
    });
    const permitEmployeeIdsMap = res?.permitEmployeeIds?.map((id) => {
      return {
        empId: id,
      };
    });
    setAuthors(permitEmployeeIdsMap);
  };

  const handleRemove = async (value?: any) => {
    const res: any = await ajax.get({
      url: `https://${HOST}/f/v2/fin-one/iec_ai_removePermission`,
      data: {
        empId: value?.empId,
        scene: delPayload?.scene,
      },
    });
    if (res?.success) {
      setModalOpen(false);
      queryScenes(delPayload?.scene);
      Modal.success({
        content: '删除成功',
      });
    }
  };

  const handleClickView = async (value: any) => {
    queryScenes(value?.scene);
    setModalOpen1(true);
    setDelPayload(value);
  };

  const handleOk = async () => {
    const res: any = await ajax.get({
      url: `https://${HOST}/f/v2/fin-one/iec_ai_addPermission`,
      data: payload,
    });
    if (res?.success) {
      setModalOpen(false);
      Modal.success({
        content: '授权成功',
      });
    }
  };

  const handleCancel = () => {
    setModalOpen(false);
  };

  const handleCancel1 = () => {
    setModalOpen1(false);
  };


  const columns: TableProps<DataType>['columns'] = [
    {
      title: '工具名称',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <a>{text}</a>,
    },
    {
      title: '负责人',
      dataIndex: 'author',
      key: 'author',
    },
    {
      title: '工具说明',
      dataIndex: 'desc',
      key: 'desc',
    },
    {
      title: '授权人',
      key: 'person',
      render: (value) => (
        <Space size="middle">
          <a onClick={() => handleClickView(value)}>查看</a>
        </Space>
      ),
    },
    {
      title: '工具类型',
      key: 'tags',
      dataIndex: 'tags',
      render: (_, { tags }) => (
        <>
          {tags.map((tag) => {
            let color = tag.length > 5 ? 'geekblue' : 'green';
            if (tag === 'loser') {
              color = 'volcano';
            }
            return (
              <Tag color={color} key={tag}>
                {tag.toUpperCase()}
              </Tag>
            );
          })}
        </>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (value) => (
        <Space size="middle">
          <a onClick={() => handleClick(value)}>授权</a>
        </Space>
      ),
    },
  ];

  const columns1: any = [
    {
      title: '授权用户ID',
      dataIndex: 'empId',
      key: 'empId',
      render: (text) => <b>{text}</b>,
    },
    {
      title: '操作',
      key: 'action',
      render: (value) => (
        <Space size="middle">
          <a onClick={() => handleRemove(value)}>移除</a>
        </Space>
      ),
    },
  ];

  const handleSelect2 = (options: any) => {
    setMy({});
    setSide(options);
  };

  const handleSelect3 = (options: any) => {
    setSide({});
    setMy(options);
  };

  const renderTable = () => {
    return <Table<DataType> columns={columns} dataSource={scenes} />;
  };

  const renderSubscribe = () => {
    return <Tabs defaultActiveKey="1" items={items} />;
  };

  const doInit = async () => {
    const res: any = await ajax.get({
      url: `https://${HOST}/f/v2/fin-one/iec_ai_getRoler`,
    });
    setAdminPayload(res);
    const listRes: any = await ajax.get({
      url: `https://${HOST}/f/v2/fin-one/iec_ai_getScenesByEmpId`,
    });
    const scenesRes: any = await ajax.get({
      url: `https://${HOST}/f/v2/fin-one/iec_ai_getScenes`,
    });
    setListPayload(listRes);
    getList(listRes?.permitScenes);
    setScenes(scenesRes?.list);
    console.log(listRes);
  };

  const getList = async (list: any) => {
    const listRes = await list?.map((item) => {
      const itemName = SCENE_MAP[item] || item;
      return getItem(itemName, itemName, <PieChartOutlined />);
    });
    setNewList(listRes);
    return listRes;
  };

  const renderAdmin = () => {
    if (my?.key === '订阅管理') {
      return renderSubscribe();
    }
    if (my?.key === '权限管理' && adminPayload?.isAdmin === true) {
      return renderTable();
    }
    if (my?.key === '权限管理' && adminPayload?.isAdmin === false) {
      return <p>抱歉暂无权限</p>;
    }
    return null;
  };

  useEffect(() => {
    doInit();
  }, []);

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider collapsible collapsed={collapsed} onCollapse={(value) => setCollapsed(value)}>
        <div className="demo-logo-vertical">
          <p className="title">场景金融AI实验室</p>
        </div>
        <Menu
          theme="dark"
          selectedKeys={[side?.key]}
          mode="inline"
          items={newList}
          onSelect={handleSelect2}
        />
        <Divider className="divider" dashed />
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[my?.key]}
          items={items2}
          onSelect={handleSelect3}
        />
      </Sider>
      <Layout>
        <Content style={{ margin: '0 16px' }}>
          <div
            style={{
              padding: 24,
              minHeight: 360,
              background: colorBgContainer,
              borderRadius: borderRadiusLG,
            }}
          >
            {renderAdmin()}
          </div>
        </Content>
        <Footer style={{ textAlign: 'center' }}>
          大淘场景金融AI实验室 ©{new Date().getFullYear()}
        </Footer>
      </Layout>
      <Modal
        title="授权"
        closable={{ 'aria-label': 'Custom Close Button' }}
        open={isModalOpen}
        onOk={handleOk}
        okText="确定"
        cancelText="取消"
        onCancel={handleCancel}
      >
        <div className="panel">
          <div className="item">
            <label>授权工具：</label>
            <p className="value">{payload?.name}</p>
          </div>
          <div className="item">
            <label>授权人（工号）：</label>
            <Input className="value" onChange={handleValueChange} />
          </div>
        </div>
      </Modal>
      <Modal
        title="已授权用户"
        closable={{ 'aria-label': 'Custom Close Button' }}
        open={isModalOpen1}
        onOk={handleOk}
        okText="确定"
        cancelText="取消"
        onCancel={handleCancel1}
      >
        <Table<DataType> columns={columns1} dataSource={authors} pagination={{ pageSize: 5 }} />
      </Modal>
    </Layout>
  );
}
