import { defineConfig } from '@ice/app';
import def from '@ali/ice-plugin-def';
import spm from '@ali/ice-plugin-spm';

// The project config, see https://ice3.alibaba-inc.com/v3/docs/guide/basic/config
const minify = process.env.NODE_ENV === 'production' ? 'swc' : false;
export default defineConfig(() => ({
  // Set your configs here.
  minify,
  server: {
    onDemand: true,
    format: 'esm',
  },
  ssr: false,
  ssg: false,
  plugins: [
    def(),
    spm(),
  ],
}));
